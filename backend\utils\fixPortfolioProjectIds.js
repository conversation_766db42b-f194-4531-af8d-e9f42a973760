#!/usr/bin/env node

/**
 * Database cleanup utility to fix inconsistent portfolio project IDs
 * This script will update all existing Visit records to use consistent projectIds
 */

require('dotenv').config();
const mongoose = require('mongoose');
const Visit = require('../models/Visit');

// Portfolio project mapping for consistent IDs
const PROJECT_ID_MAPPING = {
  // Map various title variations to consistent IDs
  '3d ecommerce': 'portfolio-0-3d-ecommerce',
  '3decommerce': 'portfolio-0-3d-ecommerce',
  'will be deployed soon': 'portfolio-1-will-be-deployed-soon',
  'willbedeployedsoon': 'portfolio-1-will-be-deployed-soon',
  'professional portfolio': 'portfolio-2-professional-portfolio',
  'professionalportfolio': 'portfolio-2-professional-portfolio',
  'available': 'portfolio-4-available',
  'experience digital banking with ai': 'portfolio-6-experience-digital-banking-with-ai',
  'experiencedigitalbankingwithai': 'portfolio-6-experience-digital-banking-with-ai',
  'hoobank super technology': 'portfolio-6-experience-digital-banking-with-ai',
  'hoobank': 'portfolio-6-experience-digital-banking-with-ai'
};

// Function to determine correct project ID based on title, image, and other data
function getCorrectProjectIdByData(visit) {
  const title = visit.projectTitle || '';
  const projectUrl = visit.projectUrl || '';
  const section = visit.section || '';
  const normalized = normalizeTitle(title);

  // 3D Ecommerce project
  if (normalized.includes('3d') || normalized.includes('ecommerce')) {
    return 'portfolio-0-3d-ecommerce';
  }

  // Professional Portfolio project
  if (normalized.includes('professional') || normalized.includes('portfolio')) {
    return 'portfolio-2-professional-portfolio';
  }

  // Available project (Flaw)
  if (normalized === 'available' || normalized.includes('flaw')) {
    return 'portfolio-4-available';
  }

  // Digital Banking AI project (consolidate Hoobank and Experience digital banking)
  if (normalized.includes('banking') || normalized.includes('hoobank') ||
      normalized.includes('digital') || normalized.includes('experience')) {
    return 'portfolio-6-experience-digital-banking-with-ai';
  }

  // "Will be deployed soon" projects - distinguish by image/URL patterns
  if (normalized.includes('deployed') || normalized.includes('soon')) {
    // Try to determine which specific "Will be deployed soon" project
    if (projectUrl.includes('ex1') || section.includes('ex1') ||
        visit.projectId === 'portfolio-1-will-be-deployed-soon') {
      return 'portfolio-1-will-be-deployed-soon';
    }
    if (projectUrl.includes('ex3') || section.includes('ex3') ||
        visit.projectId === 'portfolio-3-will-be-deployed-soon') {
      return 'portfolio-3-will-be-deployed-soon';
    }
    if (projectUrl.includes('ex5') || section.includes('ex5') ||
        visit.projectId === 'portfolio-5-will-be-deployed-soon') {
      return 'portfolio-5-will-be-deployed-soon';
    }

    // If we can't determine which one, check existing projectId pattern
    if (visit.projectId && visit.projectId.match(/portfolio-[135]-will-be-deployed-soon/)) {
      return visit.projectId;
    }

    // Default to portfolio-1 if we really can't determine
    return 'portfolio-1-will-be-deployed-soon';
  }

  return null;
}

// Function to get consistent title based on project ID
function getConsistentTitle(projectId, currentTitle) {
  switch (projectId) {
    case 'portfolio-0-3d-ecommerce':
      return '3D Ecommerce';
    case 'portfolio-1-will-be-deployed-soon':
    case 'portfolio-3-will-be-deployed-soon':
    case 'portfolio-5-will-be-deployed-soon':
      return 'Will be deployed soon.';
    case 'portfolio-2-professional-portfolio':
      return 'Professional Portfolio';
    case 'portfolio-4-available':
      return 'Available';
    case 'portfolio-6-experience-digital-banking-with-ai':
      return 'Experience digital banking with AI ';
    default:
      return currentTitle || 'Unknown Project';
  }
}

// Function to normalize title for mapping
function normalizeTitle(title) {
  if (!title) return '';
  return title.toLowerCase()
    .replace(/[^a-z0-9]/g, '')
    .trim();
}

// Function to get consistent project ID
function getConsistentProjectId(visit) {
  // Always try to determine the correct ID based on all available data
  const correctId = getCorrectProjectIdByData(visit);
  if (correctId) {
    return correctId;
  }

  // Try to map based on title using the mapping
  const normalizedTitle = normalizeTitle(visit.projectTitle);
  const mappedId = PROJECT_ID_MAPPING[normalizedTitle];

  if (mappedId) {
    return mappedId;
  }

  // If we have a valid portfolio ID that follows the correct pattern, keep it
  if (visit.projectId && visit.projectId.match(/^portfolio-[0-6]-/)) {
    return visit.projectId;
  }

  return 'portfolio-1-will-be-deployed-soon'; // Default fallback
}

async function fixPortfolioProjectIds() {
  try {
    console.log('🔧 Starting portfolio project ID cleanup...');

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI || 'mongodb+srv://Aminos:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster69');
    console.log('✅ Connected to MongoDB');

    // Find all portfolio-related visits
    const portfolioVisits = await Visit.find({
      $or: [
        { projectType: 'portfolio-carousel' },
        { section: { $regex: /^portfolio-item/ } },
        { section: { $regex: /^project-interaction/ } },
        { projectId: { $regex: /^portfolio-/ } },
        { projectTitle: { $exists: true, $ne: null } }
      ]
    });

    console.log(`📊 Found ${portfolioVisits.length} portfolio-related visits to process`);

    let updatedCount = 0;
    const updatePromises = [];

    // Process each visit
    for (const visit of portfolioVisits) {
      const currentProjectId = visit.projectId;
      const projectTitle = visit.projectTitle;

      let newProjectId = getConsistentProjectId(visit);

      // Always update if we have a better ID or if the current ID doesn't follow our pattern
      const shouldUpdate = newProjectId && (
        newProjectId !== currentProjectId ||
        !currentProjectId ||
        !currentProjectId.match(/^portfolio-[0-6]-[a-z-]+$/)
      );

      if (shouldUpdate) {
        updatePromises.push(
          Visit.updateOne(
            { _id: visit._id },
            {
              $set: {
                projectId: newProjectId,
                // Also ensure projectTitle is consistent based on project type
                projectTitle: getConsistentTitle(newProjectId, projectTitle)
              }
            }
          )
        );
        updatedCount++;
      }
    }

    // Execute all updates
    if (updatePromises.length > 0) {
      console.log(`🔄 Updating ${updatePromises.length} records...`);
      await Promise.all(updatePromises);
      console.log(`✅ Successfully updated ${updatedCount} portfolio project records`);
    } else {
      console.log('ℹ️ No records needed updating');
    }

    // Show summary of current state
    const summary = await Visit.aggregate([
      {
        $match: {
          $or: [
            { projectType: 'portfolio-carousel' },
            { projectId: { $regex: /^portfolio-/ } }
          ]
        }
      },
      {
        $group: {
          _id: '$projectId',
          count: { $sum: 1 },
          titles: { $addToSet: '$projectTitle' }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);

    console.log('\n📈 Current portfolio project distribution:');
    summary.forEach(item => {
      console.log(`  ${item._id}: ${item.count} records (titles: ${item.titles.join(', ')})`);
    });

    console.log('\n🎉 Portfolio project ID cleanup completed!');

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the cleanup if this script is executed directly
if (require.main === module) {
  fixPortfolioProjectIds();
}

module.exports = { fixPortfolioProjectIds };
