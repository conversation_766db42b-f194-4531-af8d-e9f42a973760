#!/usr/bin/env node

/**
 * <PERSON>ript to check current portfolio tracking data in the database
 */

require('dotenv').config();
const mongoose = require('mongoose');
const Visit = require('../models/Visit');

async function checkPortfolioData() {
  try {
    console.log('🔍 Checking portfolio tracking data...');

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI || 'mongodb+srv://Aminos:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster69');
    console.log('✅ Connected to MongoDB');

    // Check all portfolio-related visits
    const portfolioVisits = await Visit.find({
      $or: [
        { projectType: 'portfolio-carousel' },
        { section: { $regex: /^portfolio-item/ } },
        { section: { $regex: /^project-interaction/ } },
        { projectId: { $regex: /^portfolio-/ } }
      ]
    }).select('projectId projectTitle projectUrl section interactionType timestamp');

    console.log(`📊 Total portfolio visits: ${portfolioVisits.length}`);

    // Group by projectId
    const groupedData = {};
    portfolioVisits.forEach(visit => {
      const id = visit.projectId || 'unknown';
      if (!groupedData[id]) {
        groupedData[id] = {
          count: 0,
          titles: new Set(),
          urls: new Set(),
          sections: new Set(),
          interactions: new Set()
        };
      }
      groupedData[id].count++;
      if (visit.projectTitle) groupedData[id].titles.add(visit.projectTitle);
      if (visit.projectUrl) groupedData[id].urls.add(visit.projectUrl);
      if (visit.section) groupedData[id].sections.add(visit.section);
      if (visit.interactionType) groupedData[id].interactions.add(visit.interactionType);
    });

    console.log('\n📈 Portfolio project breakdown:');
    Object.keys(groupedData).sort().forEach(projectId => {
      const data = groupedData[projectId];
      console.log(`\n🔸 ${projectId}: ${data.count} records`);
      console.log(`   Titles: ${Array.from(data.titles).join(', ')}`);
      console.log(`   URLs: ${Array.from(data.urls).join(', ')}`);
      console.log(`   Sections: ${Array.from(data.sections).slice(0, 3).join(', ')}${data.sections.size > 3 ? '...' : ''}`);
      console.log(`   Interactions: ${Array.from(data.interactions).join(', ')}`);
    });

    // Check for any visits that might contain ex3 or ex5 references
    console.log('\n🔍 Searching for ex3/ex5 references...');
    const ex3Visits = await Visit.find({
      $or: [
        { projectUrl: { $regex: /ex3/i } },
        { section: { $regex: /ex3/i } },
        { projectTitle: { $regex: /ex3/i } }
      ]
    }).select('projectId projectTitle projectUrl section');

    const ex5Visits = await Visit.find({
      $or: [
        { projectUrl: { $regex: /ex5/i } },
        { section: { $regex: /ex5/i } },
        { projectTitle: { $regex: /ex5/i } }
      ]
    }).select('projectId projectTitle projectUrl section');

    console.log(`📸 Found ${ex3Visits.length} visits with ex3 references`);
    ex3Visits.slice(0, 5).forEach(visit => {
      console.log(`   - ${visit.projectId}: ${visit.projectTitle} | ${visit.projectUrl} | ${visit.section}`);
    });

    console.log(`📸 Found ${ex5Visits.length} visits with ex5 references`);
    ex5Visits.slice(0, 5).forEach(visit => {
      console.log(`   - ${visit.projectId}: ${visit.projectTitle} | ${visit.projectUrl} | ${visit.section}`);
    });

    console.log('\n✅ Portfolio data check completed!');

  } catch (error) {
    console.error('❌ Error during check:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the check if this script is executed directly
if (require.main === module) {
  checkPortfolioData();
}

module.exports = { checkPortfolioData };
