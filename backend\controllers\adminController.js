const Admin = require('../models/Admin');
const Visit = require('../models/Visit');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { batchGetCountriesFromIPs } = require('../utils/geolocation');
const { validationResult } = require('express-validator');

// POST /api/admin/login
exports.login = async (req, res) => {
  // Sanitize input
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ message: 'Invalid input' });
  }
  const { email, password } = req.body;
  try {
    // Find admin in database
    const admin = await Admin.findOne({ email });
    if (!admin) {
      console.log(`[LOGIN FAIL] IP: ${req.visitorIP}, Time: ${new Date().toISOString()}, Reason: admin not found`);
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Compare password with hashed password from database
    const isMatch = await bcrypt.compare(password, admin.password);
    if (!isMatch) {
      console.log(`[LOGIN FAIL] IP: ${req.visitorIP}, Time: ${new Date().toISOString()}, Reason: wrong password`);
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    const token = jwt.sign({ email }, process.env.JWT_SECRET, { expiresIn: '1h' });
    console.log(`[LOGIN SUCCESS] IP: ${req.visitorIP}, Time: ${new Date().toISOString()}, Email: ${email}`);
    res.json({ token });
  } catch (err) {
    console.log('LOGIN ERROR:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// GET /api/admin/dashboard
exports.dashboard = async (req, res) => {
  try {
    // Get total visits and unique visitors
    const totalVisits = await Visit.countDocuments();
    const uniqueVisitors = await Visit.distinct('ip').then(ips => ips.length);

    // Calculate daily visitor statistics using Tunisia timezone (Africa/Tunis)
    // This ensures "today" is calculated based on the user's actual timezone, not server timezone
    const now = new Date();

    // Get current date in Tunisia timezone
    const tunisiaDateString = now.toLocaleDateString('en-CA', { timeZone: 'Africa/Tunis' }); // YYYY-MM-DD format
    const today = new Date(tunisiaDateString + 'T00:00:00.000Z');
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    console.log(`🕐 Timezone Debug - Server UTC: ${now.toISOString()}, Tunisia Date: ${tunisiaDateString}, Today Range: ${today.toISOString()} to ${tomorrow.toISOString()}`);

    // Get today's unique visitors
    const todayUniqueVisitors = await Visit.distinct('ip', {
      timestamp: { $gte: today, $lt: tomorrow }
    });
    const todayVisitorCount = todayUniqueVisitors.length;

    // Get yesterday's unique visitors for comparison (also in Tunisia timezone)
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayUniqueVisitors = await Visit.distinct('ip', {
      timestamp: { $gte: yesterday, $lt: today }
    });
    const yesterdayVisitorCount = yesterdayUniqueVisitors.length;

    // Calculate daily visitor statistics for the last 30 days
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const dailyStats = await Visit.aggregate([
      {
        $match: {
          timestamp: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$timestamp' },
            month: { $month: '$timestamp' },
            day: { $dayOfMonth: '$timestamp' }
          },
          uniqueVisitors: { $addToSet: '$ip' },
          totalVisits: { $sum: 1 }
        }
      },
      {
        $addFields: {
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: '$_id.day'
            }
          },
          uniqueVisitorCount: { $size: '$uniqueVisitors' }
        }
      },
      {
        $sort: { date: -1 }
      }
    ]);

    // Get all visits with more details (removed limit to show all historical data)
    const recentVisits = await Visit.find()
      .sort({ timestamp: -1 })
      .select('ip timestamp section duration pageUrl');

    // Aggregate section stats based on total duration spent
    const sectionDurationStats = await Visit.aggregate([
      {
        $group: {
          _id: '$section',
          totalDuration: { $sum: '$duration' },
          visitCount: { $sum: 1 },
          avgDuration: { $avg: '$duration' },
          uniqueVisitors: { $addToSet: '$ip' }
        }
      },
      {
        $addFields: {
          uniqueVisitorCount: { $size: '$uniqueVisitors' }
        }
      },
      {
        $sort: { totalDuration: -1 }
      }
    ]);

    // Calculate total duration across all sections for percentage calculation
    const totalDurationAllSections = sectionDurationStats.reduce(
      (sum, section) => sum + section.totalDuration, 0
    );

    // Format section stats with duration-based percentages
    const statsWithPercent = sectionDurationStats.map(s => ({
      section: s._id,
      count: s.visitCount,
      totalDuration: Math.round(s.totalDuration),
      avgDuration: Math.round(s.avgDuration),
      uniqueVisitors: s.uniqueVisitorCount,
      percent: totalDurationAllSections > 0
        ? ((s.totalDuration / totalDurationAllSections) * 100).toFixed(1)
        : 0
    }));

    // Format recent visits for display
    const formattedVisits = recentVisits.map(v => ({
      ip: v.ip,
      timestamp: v.timestamp,
      section: v.section,
      duration: Math.round(v.duration || 0),
      pageUrl: v.pageUrl || ''
    }));

    // Calculate average session duration per unique visitor
    const sessionDurations = await Visit.aggregate([
      {
        $group: {
          _id: '$ip',
          totalSessionDuration: { $sum: '$duration' }
        }
      }
    ]);

    const avgSessionDuration = sessionDurations.length > 0
      ? Math.round(sessionDurations.reduce((sum, session) => sum + session.totalSessionDuration, 0) / sessionDurations.length)
      : 0;

    res.json({
      totalVisits,
      uniqueVisitors,
      visits: formattedVisits,
      sectionStats: statsWithPercent,
      dailyStats: {
        todayVisitors: todayVisitorCount,
        yesterdayVisitors: yesterdayVisitorCount,
        dailyBreakdown: dailyStats.map(stat => ({
          date: stat.date,
          uniqueVisitors: stat.uniqueVisitorCount,
          totalVisits: stat.totalVisits
        }))
      },
      summary: {
        totalDurationAllSections: Math.round(totalDurationAllSections),
        avgSessionDuration: avgSessionDuration
      },
      // Debug info for timezone fix verification
      debug: {
        serverTime: now.toISOString(),
        tunisiaDate: tunisiaDateString,
        todayRange: `${today.toISOString()} to ${tomorrow.toISOString()}`,
        todayUniqueIPs: todayUniqueVisitors.slice(0, 5) // Show first 5 IPs for debugging
      }
    });
  } catch (err) {
    console.error('Dashboard error:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// GET /api/admin/timezone-test - Test endpoint to debug timezone issues
exports.timezoneTest = async (req, res) => {
  try {
    const now = new Date();
    const tunisiaDateString = now.toLocaleDateString('en-CA', { timeZone: 'Africa/Tunis' });
    const today = new Date(tunisiaDateString + 'T00:00:00.000Z');
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Get some recent visits for testing
    const recentVisits = await Visit.find()
      .sort({ timestamp: -1 })
      .limit(10)
      .select('ip timestamp section');

    // Get today's visits using the new timezone logic
    const todayVisits = await Visit.find({
      timestamp: { $gte: today, $lt: tomorrow }
    }).select('ip timestamp section');

    res.json({
      serverInfo: {
        serverTime: now.toISOString(),
        serverTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        tunisiaTime: now.toLocaleString('en-US', { timeZone: 'Africa/Tunis' }),
        tunisiaDate: tunisiaDateString
      },
      dateRanges: {
        todayStart: today.toISOString(),
        todayEnd: tomorrow.toISOString(),
        todayStartTunisia: today.toLocaleString('en-US', { timeZone: 'Africa/Tunis' }),
        todayEndTunisia: tomorrow.toLocaleString('en-US', { timeZone: 'Africa/Tunis' })
      },
      visitCounts: {
        totalVisits: await Visit.countDocuments(),
        todayVisitsCount: todayVisits.length,
        todayUniqueIPs: [...new Set(todayVisits.map(v => v.ip))].length,
        recentVisitsCount: recentVisits.length
      },
      sampleData: {
        recentVisits: recentVisits.map(v => ({
          ip: v.ip,
          timestamp: v.timestamp.toISOString(),
          timestampTunisia: v.timestamp.toLocaleString('en-US', { timeZone: 'Africa/Tunis' }),
          section: v.section
        })),
        todayVisits: todayVisits.slice(0, 5).map(v => ({
          ip: v.ip,
          timestamp: v.timestamp.toISOString(),
          timestampTunisia: v.timestamp.toLocaleString('en-US', { timeZone: 'Africa/Tunis' }),
          section: v.section
        }))
      }
    });
  } catch (err) {
    console.error('Timezone test error:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
};

// POST /api/admin/geolocation - Get geolocation data for IPs
exports.getGeolocation = async (req, res) => {
  try {
    const { ips } = req.body;

    if (!ips || !Array.isArray(ips)) {
      return res.status(400).json({ message: 'IPs array is required' });
    }

    console.log(`Geolocation request for ${ips.length} IPs`);

    // Process geolocation lookup
    const geoData = await batchGetCountriesFromIPs(ips);

    res.json({
      success: true,
      data: geoData,
      processedCount: Object.keys(geoData).length
    });

  } catch (error) {
    console.error('Geolocation error:', error);
    res.status(500).json({
      success: false,
      message: 'Geolocation lookup failed',
      error: error.message
    });
  }
};

// GET /api/admin/experience-projects-analytics
const { getExperienceProjectIdentifiers, isValidExperienceProject } = require('../config/projectsConfig');

exports.getExperienceProjectsAnalytics = async (req, res) => {
  try {
    // Get valid project identifiers from centralized configuration
    const { slugs: validSlugs, titles: validTitles } = getExperienceProjectIdentifiers();

    // Get analytics for experience section projects (job detail pages)
    const experienceStats = await Visit.aggregate([
      {
        $match: {
          $or: [
            { section: { $regex: /^job-detail/ } },
            { jobSlug: { $in: validSlugs } },
            { jobTitle: { $in: validTitles } },
            { projectType: 'experience' }
          ]
        }
      },
      {
        $group: {
          _id: {
            jobSlug: '$jobSlug',
            jobTitle: '$jobTitle'
          },
          totalViews: { $sum: 1 },
          totalDuration: { $sum: '$duration' },
          avgDuration: { $avg: '$duration' },
          uniqueVisitors: { $addToSet: '$ip' },
          lastVisit: { $max: '$timestamp' },
          interactions: {
            $push: {
              ip: '$ip',
              timestamp: '$timestamp',
              duration: '$duration',
              section: '$section',
              interactionType: '$interactionType',
              projectTitle: '$projectTitle'
            }
          }
        }
      },
      {
        $addFields: {
          uniqueVisitorCount: { $size: '$uniqueVisitors' }
        }
      },
      {
        $sort: { totalViews: -1 }
      }
    ]);

    // Format the response and filter to only valid experience projects
    const formattedStats = experienceStats
      .map(stat => ({
        jobSlug: stat._id.jobSlug || 'unknown',
        jobTitle: stat._id.jobTitle || 'Unknown Job',
        totalViews: stat.totalViews,
        totalDuration: Math.round(stat.totalDuration),
        avgDuration: Math.round(stat.avgDuration),
        uniqueVisitorCount: stat.uniqueVisitorCount,
        lastVisit: stat.lastVisit,
        recentInteractions: stat.interactions
          .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
          .slice(0, 10) // Last 10 interactions
      }))
      .filter(stat => {
        // Use centralized validation function
        return isValidExperienceProject(stat.jobSlug, stat.jobTitle);
      })
      .slice(0, 3); // Ensure maximum of 3 projects

    res.json({
      success: true,
      data: formattedStats,
      totalProjects: formattedStats.length,
      summary: {
        totalViews: formattedStats.reduce((sum, stat) => sum + stat.totalViews, 0),
        totalDuration: formattedStats.reduce((sum, stat) => sum + stat.totalDuration, 0),
        avgViewsPerProject: formattedStats.length > 0 ?
          Math.round(formattedStats.reduce((sum, stat) => sum + stat.totalViews, 0) / formattedStats.length) : 0
      }
    });

  } catch (error) {
    console.error('Experience projects analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch experience projects analytics',
      error: error.message
    });
  }
};

// GET /api/admin/portfolio-projects-analytics
const { getPortfolioProjectIdentifiers, isValidPortfolioProject } = require('../config/projectsConfig');

exports.getPortfolioProjectsAnalytics = async (req, res) => {
  try {
    // Get valid project identifiers from centralized configuration
    const { projectIds: validProjectIds, titles: validTitles } = getPortfolioProjectIdentifiers();

    // Get analytics for portfolio carousel projects
    const portfolioStats = await Visit.aggregate([
      {
        $match: {
          $or: [
            { projectType: 'portfolio-carousel' },
            { section: { $regex: /^portfolio-item/ } },
            { section: { $regex: /^project-interaction/ } },
            { projectId: { $in: validProjectIds } },
            { projectTitle: { $in: validTitles } }
          ]
        }
      },
      {
        // Add a field to normalize project identification with better logic
        $addFields: {
          normalizedProjectId: {
            $cond: {
              if: { $and: [{ $ne: ['$projectId', null] }, { $ne: ['$projectId', ''] }, { $ne: ['$projectId', 'unknown'] }] },
              then: '$projectId',
              else: 'unknown'
            }
          }
        }
      },
      {
        $group: {
          _id: '$normalizedProjectId',
          projectId: { $first: '$projectId' },
          projectTitle: { $first: '$projectTitle' },
          totalInteractions: { $sum: 1 },
          totalDuration: { $sum: '$duration' },
          avgDuration: { $avg: '$duration' },
          uniqueVisitors: { $addToSet: '$ip' },
          lastInteraction: { $max: '$timestamp' },
          interactionTypes: { $addToSet: '$interactionType' },
          availabilityStatus: { $first: '$projectAvailable' },
          projectUrl: { $first: '$projectUrl' },
          interactions: {
            $push: {
              ip: '$ip',
              timestamp: '$timestamp',
              duration: '$duration',
              interactionType: '$interactionType',
              section: '$section',
              projectAvailable: '$projectAvailable'
            }
          }
        }
      },
      {
        $addFields: {
          uniqueVisitorCount: { $size: '$uniqueVisitors' },
          interactionTypeCount: { $size: '$interactionTypes' }
        }
      },
      {
        $sort: { totalInteractions: -1 }
      }
    ]);

    // Format the response and filter to only valid portfolio projects
    const formattedStats = portfolioStats
      .map(stat => ({
        projectId: stat.projectId || 'unknown',
        projectTitle: stat.projectTitle || 'Unknown Project',
        totalInteractions: stat.totalInteractions,
        totalDuration: Math.round(stat.totalDuration),
        avgDuration: Math.round(stat.avgDuration),
        uniqueVisitorCount: stat.uniqueVisitorCount,
        lastInteraction: stat.lastInteraction,
        interactionTypes: stat.interactionTypes.filter(type => type && type.trim() !== ''),
        availabilityStatus: stat.availabilityStatus,
        projectUrl: stat.projectUrl || '',
        recentInteractions: stat.interactions
          .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
          .slice(0, 10) // Last 10 interactions
      }))
      .filter(stat => {
        // Use centralized validation function
        return isValidPortfolioProject(stat.projectId, stat.projectTitle);
      });

    // Remove duplicates based on exact project ID, keeping the one with most interactions
    const uniqueProjects = [];
    const seenProjects = new Set();

    formattedStats
      .sort((a, b) => b.totalInteractions - a.totalInteractions) // Sort by interactions desc
      .forEach(project => {
        // Use exact projectId as the key for deduplication
        const key = project.projectId && project.projectId !== 'unknown' ?
          project.projectId : `title-${project.projectTitle}`;

        if (!seenProjects.has(key)) {
          seenProjects.add(key);
          uniqueProjects.push(project);
        }
      });

    const finalStats = uniqueProjects.slice(0, 7); // Ensure maximum of 7 projects

    // Separate available and unavailable projects from deduplicated data
    const availableProjects = finalStats.filter(stat => stat.availabilityStatus !== false);
    const unavailableProjects = finalStats.filter(stat => stat.availabilityStatus === false);

    res.json({
      success: true,
      data: finalStats,
      availableProjects,
      unavailableProjects,
      totalProjects: finalStats.length,
      summary: {
        totalInteractions: finalStats.reduce((sum, stat) => sum + stat.totalInteractions, 0),
        totalDuration: finalStats.reduce((sum, stat) => sum + stat.totalDuration, 0),
        avgInteractionsPerProject: finalStats.length > 0 ?
          Math.round(finalStats.reduce((sum, stat) => sum + stat.totalInteractions, 0) / finalStats.length) : 0,
        availableProjectsCount: availableProjects.length,
        unavailableProjectsCount: unavailableProjects.length
      }
    });

  } catch (error) {
    console.error('Portfolio projects analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch portfolio projects analytics',
      error: error.message
    });
  }
};