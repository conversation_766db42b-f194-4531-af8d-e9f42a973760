{"name": "portfolio-pro-monorepo", "version": "1.0.0", "description": "Portfolio Pro - Full Stack Application", "scripts": {"install-backend": "cd backend && npm install", "install-frontend": "cd portfolio-react && npm install", "install-all": "npm run install-backend && npm run install-frontend", "build-backend": "cd backend && npm install", "build-frontend": "cd portfolio-react && npm install && npm run build", "start-backend": "cd backend && npm start", "start-frontend": "cd portfolio-react && npm run serve"}, "dependencies": {"bcryptjs": "^3.0.2"}}