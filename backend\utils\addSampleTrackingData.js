#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to add sample tracking data for missing portfolio projects
 */

require('dotenv').config();
const mongoose = require('mongoose');
const Visit = require('../models/Visit');

async function addSampleTrackingData() {
  try {
    console.log('📊 Adding sample tracking data for missing portfolio projects...');

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI || 'mongodb+srv://Aminos:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster69');
    console.log('✅ Connected to MongoDB');

    const now = new Date();
    const sampleIPs = ['*************', '*********', '***********', '************', '*************'];

    // Sample tracking data for portfolio-3-will-be-deployed-soon (ex3.webp)
    const portfolio3Data = [];
    for (let i = 0; i < 15; i++) {
      const timestamp = new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000); // Random time in last 7 days
      portfolio3Data.push({
        ip: sampleIPs[Math.floor(Math.random() * sampleIPs.length)],
        timestamp: timestamp,
        section: 'portfolio-item-hover',
        duration: Math.floor(Math.random() * 5000) + 1000, // 1-6 seconds
        projectType: 'portfolio-carousel',
        projectId: 'portfolio-3-will-be-deployed-soon',
        projectTitle: 'Will be deployed soon.',
        projectAvailable: false,
        interactionType: 'hover',
        projectUrl: '#'
      });
    }

    // Add some click interactions for portfolio-3
    for (let i = 0; i < 8; i++) {
      const timestamp = new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000);
      portfolio3Data.push({
        ip: sampleIPs[Math.floor(Math.random() * sampleIPs.length)],
        timestamp: timestamp,
        section: 'portfolio-item-unavailable-click',
        duration: Math.floor(Math.random() * 2000) + 500,
        projectType: 'portfolio-carousel',
        projectId: 'portfolio-3-will-be-deployed-soon',
        projectTitle: 'Will be deployed soon.',
        projectAvailable: false,
        interactionType: 'unavailable-click',
        projectUrl: '#'
      });
    }

    // Sample tracking data for portfolio-5-will-be-deployed-soon (ex5.png)
    const portfolio5Data = [];
    for (let i = 0; i < 12; i++) {
      const timestamp = new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000);
      portfolio5Data.push({
        ip: sampleIPs[Math.floor(Math.random() * sampleIPs.length)],
        timestamp: timestamp,
        section: 'portfolio-item-hover',
        duration: Math.floor(Math.random() * 4000) + 800,
        projectType: 'portfolio-carousel',
        projectId: 'portfolio-5-will-be-deployed-soon',
        projectTitle: 'Will be deployed soon.',
        projectAvailable: false,
        interactionType: 'hover',
        projectUrl: '#'
      });
    }

    // Add some click interactions for portfolio-5
    for (let i = 0; i < 6; i++) {
      const timestamp = new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000);
      portfolio5Data.push({
        ip: sampleIPs[Math.floor(Math.random() * sampleIPs.length)],
        timestamp: timestamp,
        section: 'portfolio-item-unavailable-click',
        duration: Math.floor(Math.random() * 1500) + 400,
        projectType: 'portfolio-carousel',
        projectId: 'portfolio-5-will-be-deployed-soon',
        projectTitle: 'Will be deployed soon.',
        projectAvailable: false,
        interactionType: 'unavailable-click',
        projectUrl: '#'
      });
    }

    // Insert the sample data
    const allSampleData = [...portfolio3Data, ...portfolio5Data];
    await Visit.insertMany(allSampleData);

    console.log(`✅ Added ${portfolio3Data.length} tracking records for portfolio-3-will-be-deployed-soon`);
    console.log(`✅ Added ${portfolio5Data.length} tracking records for portfolio-5-will-be-deployed-soon`);
    console.log(`📊 Total sample records added: ${allSampleData.length}`);

    // Verify the data was added
    const portfolio3Count = await Visit.countDocuments({ projectId: 'portfolio-3-will-be-deployed-soon' });
    const portfolio5Count = await Visit.countDocuments({ projectId: 'portfolio-5-will-be-deployed-soon' });

    console.log(`\n📈 Current counts after adding sample data:`);
    console.log(`   portfolio-3-will-be-deployed-soon: ${portfolio3Count} records`);
    console.log(`   portfolio-5-will-be-deployed-soon: ${portfolio5Count} records`);

    console.log('\n🎉 Sample tracking data added successfully!');

  } catch (error) {
    console.error('❌ Error adding sample data:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script if executed directly
if (require.main === module) {
  addSampleTrackingData();
}

module.exports = { addSampleTrackingData };
