import React, { useEffect, Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './App.css';
import Home from './components/Home';
import JobDetail from './components/JobDetail';
import ScrollToTopButton from './components/ScrollToTopButton';
import { initializeBackendWakeup } from './utils/backendWakeup';
import { initializeExtensionErrorHandling, filterExtensionConsoleErrors } from './utils/extensionErrorHandler';
import {
  initializeAdvancedExtensionInterception,
  protectCriticalAPIs
} from './utils/extensionErrorInterceptor';

// Lazy load admin components to reduce initial bundle size
const AdminLogin = lazy(() => import('./components/AdminLogin'));
const AdminDashboard = lazy(() => import('./components/AdminDashboard'));
const SectionDetailsAnalysis = lazy(() => import('./components/SectionDetailsAnalysis'));
const VisitorDetails = lazy(() => import('./components/VisitorDetails'));
const AllVisitorsDetails = lazy(() => import('./components/AllVisitorsDetails'));
const ExperienceProjectsAnalytics = lazy(() => import('./components/ExperienceProjectsAnalytics'));
const PortfolioProjectsAnalytics = lazy(() => import('./components/PortfolioProjectsAnalytics'));



function App() {
  // Initialize backend wakeup and extension error handling when app loads
  useEffect(() => {
    // Initialize all extension error protection layers
    initializeExtensionErrorHandling(); // Basic extension error handling
    filterExtensionConsoleErrors(); // Filter console spam from extensions
    initializeAdvancedExtensionInterception(); // Advanced interception
    protectCriticalAPIs(); // Protect localStorage, sessionStorage, etc.

    // Initialize backend wakeup after error handling is set up
    initializeBackendWakeup();
  }, []); // Empty dependency array means this runs once on mount

  return (
    <Router>
      <div className="App">
        <Suspense fallback={
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100vh',
            color: '#FFFFFF',
            fontSize: '18px'
          }}>
            Loading...
          </div>
        }>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/job/:slug" element={<JobDetail />} />
            <Route path="/admin/login" element={<AdminLogin />} />
            <Route path="/admin/dashboard" element={<AdminDashboard />} />
            <Route path="/admin/section-details" element={<SectionDetailsAnalysis />} />
            <Route path="/admin/visitor/:visitorIp" element={<VisitorDetails />} />
            <Route path="/admin/all-visitors" element={<AllVisitorsDetails />} />
            <Route path="/admin/experience-analytics" element={<ExperienceProjectsAnalytics />} />
            <Route path="/admin/portfolio-analytics" element={<PortfolioProjectsAnalytics />} />
          </Routes>
        </Suspense>
        <ScrollToTopButton />
      </div>
    </Router>
  );
}

export default App;
